fileFormatVersion: 2
guid: 9ec52349a51f4191b990ac6c1f7ac130
labels:
- gvh
- gvh_version-11.8.1
- gvhp_exportpath-Firebase/Plugins/x86_64/FirebaseCppDatabase.bundle
timeCreated: 1480838400
PluginImporter:
  serializedVersion: 1
  iconMap: {}
  executionOrder: {}
  isPreloaded: 0
  platformData:
    Android:
      enabled: 0
      settings:
        CPU: AnyCPU
    Any:
      enabled: 0
      settings: {}
    Editor:
      enabled: 1
      settings:
        CPU: AnyCPU
        DefaultValueInitialized: true
        OS: AnyOS
    Linux:
      enabled: 0
      settings:
        CPU: None
    Linux64:
      enabled: 0
      settings:
        CPU: None
    LinuxUniversal:
      enabled: 0
      settings:
        CPU: None
    OSXIntel:
      enabled: 0
      settings:
        CPU: None
    OSXIntel64:
      enabled: 1
      settings:
        CPU: x86_64
    OSXUniversal:
      enabled: 1
      settings:
        CPU: AnyCPU
    Web:
      enabled: 0
      settings: {}
    WebStreamed:
      enabled: 0
      settings: {}
    Win:
      enabled: 0
      settings:
        CPU: None
    Win64:
      enabled: 0
      settings:
        CPU: None
    WindowsStoreApps:
      enabled: 0
      settings:
        CPU: AnyCPU
    iOS:
      enabled: 0
      settings:
        CompileFlags:
        FrameworkDependencies:
    tvOS:
      enabled: 0
      settings:
        CompileFlags:
        FrameworkDependencies:
  userData:
  assetBundleName:
  assetBundleVariant:
