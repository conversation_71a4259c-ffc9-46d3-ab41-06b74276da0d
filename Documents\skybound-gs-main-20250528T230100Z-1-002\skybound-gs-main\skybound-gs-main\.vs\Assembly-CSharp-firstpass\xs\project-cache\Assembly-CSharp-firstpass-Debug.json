{"Format": 1, "ProjectReferences": [], "MetadataReferences": [{"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/Microsoft.Win32.Primitives.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/mscorlib.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/netstandard.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.AppContext.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.Collections.Concurrent.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.Collections.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.Collections.NonGeneric.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.Collections.Specialized.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/System.ComponentModel.Composition.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.ComponentModel.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.ComponentModel.EventBasedAsync.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.ComponentModel.Primitives.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.ComponentModel.TypeConverter.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.Console.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/System.Core.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.Data.Common.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/System.Data.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.Diagnostics.Contracts.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.Diagnostics.Debug.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.Diagnostics.FileVersionInfo.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.Diagnostics.Process.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.Diagnostics.StackTrace.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.Diagnostics.TextWriterTraceListener.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.Diagnostics.Tools.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.Diagnostics.TraceSource.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/System.Diagnostics.Tracing.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/System.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/System.Drawing.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.Drawing.Primitives.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.Dynamic.Runtime.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.Globalization.Calendars.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.Globalization.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.Globalization.Extensions.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/System.IO.Compression.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/System.IO.Compression.FileSystem.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.IO.Compression.ZipFile.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.IO.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.DriveInfo.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.Primitives.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.Watcher.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.IO.IsolatedStorage.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.IO.MemoryMappedFiles.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.IO.Pipes.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.IO.UnmanagedMemoryStream.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.Linq.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.Linq.Expressions.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.Linq.Parallel.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.Linq.Queryable.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/System.Net.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/System.Net.Http.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.Net.NameResolution.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.Net.NetworkInformation.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.Net.Ping.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.Net.Primitives.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.Net.Requests.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.Net.Security.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.Net.Sockets.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.Net.WebHeaderCollection.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.Net.WebSockets.Client.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.Net.WebSockets.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/System.Numerics.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/System.Numerics.Vectors.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.ObjectModel.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.Reflection.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.Reflection.Extensions.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.Reflection.Primitives.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.Resources.Reader.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.Resources.ResourceManager.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.Resources.Writer.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.Runtime.CompilerServices.VisualC.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.Runtime.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.Runtime.Extensions.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.Runtime.Handles.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.Runtime.InteropServices.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.Runtime.InteropServices.RuntimeInformation.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.Runtime.InteropServices.WindowsRuntime.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.Runtime.Numerics.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/System.Runtime.Serialization.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Formatters.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Json.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Primitives.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Xml.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.Security.Claims.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Algorithms.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Csp.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Encoding.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Primitives.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.X509Certificates.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.Security.Principal.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.Security.SecureString.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/System.ServiceModel.Web.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.Text.Encoding.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.Text.Encoding.Extensions.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.Text.RegularExpressions.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.Threading.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.Threading.Overlapped.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.Threading.Tasks.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.Threading.Tasks.Parallel.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.Threading.Thread.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.Threading.ThreadPool.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.Threading.Timer.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/System.Transactions.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.ValueTuple.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/System.Web.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/System.Windows.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/System.Xml.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/System.Xml.Linq.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.Xml.ReaderWriter.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/System.Xml.Serialization.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.Xml.XDocument.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.Xml.XmlDocument.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.Xml.XmlSerializer.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.Xml.XPath.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Library/Frameworks/Mono.framework/Versions/6.12.0/lib/mono/4.7.1-api/Facades/System.Xml.XPath.XDocument.dll", "Aliases": [], "Framework": null}], "Files": ["/Users/<USER>/Downloads/skybound-gs-main/skybound-gs-main/Assets/Plugins/Demigiant/DOTween/Modules/DOTweenModulePhysics2D.cs", "/Users/<USER>/Downloads/skybound-gs-main/skybound-gs-main/Assets/Plugins/Demigiant/DOTween/Modules/DOTweenModulePhysics.cs", "/Users/<USER>/Downloads/skybound-gs-main/skybound-gs-main/Assets/Plugins/Demigiant/DOTween/Modules/DOTweenModuleUnityVersion.cs", "/Users/<USER>/Downloads/skybound-gs-main/skybound-gs-main/Assets/Plugins/Demigiant/DOTween/Modules/DOTweenModuleEPOOutline.cs", "/Users/<USER>/Downloads/skybound-gs-main/skybound-gs-main/Assets/Plugins/Demigiant/DOTween/Modules/DOTweenModuleSprite.cs", "/Users/<USER>/Downloads/skybound-gs-main/skybound-gs-main/Assets/Plugins/Demigiant/DOTween/Modules/DOTweenModuleUtils.cs", "/Users/<USER>/Downloads/skybound-gs-main/skybound-gs-main/Assets/Plugins/Demigiant/DOTween/Modules/DOTweenModuleUI.cs", "/Users/<USER>/Downloads/skybound-gs-main/skybound-gs-main/Assets/Plugins/Demigiant/DOTween/Modules/DOTweenModuleAudio.cs", "/Users/<USER>/Downloads/skybound-gs-main/skybound-gs-main/Assets/Plugins/Android/FirebaseApp.androidlib/AndroidManifest.xml", "/Users/<USER>/Downloads/skybound-gs-main/skybound-gs-main/Assets/Plugins/Android/FirebaseCrashlytics.androidlib/AndroidManifest.xml", "/Users/<USER>/Downloads/skybound-gs-main/skybound-gs-main/Assets/Plugins/Android/FirebaseCrashlytics.androidlib/res/values/crashlytics_build_id.xml", "/Users/<USER>/Downloads/skybound-gs-main/skybound-gs-main/Assets/Plugins/Android/GoogleMobileAdsPlugin.androidlib/AndroidManifest.xml", "/Users/<USER>/Downloads/skybound-gs-main/skybound-gs-main/Assets/Plugins/Android/FirebaseCrashlytics.androidlib/res/values/crashlytics_unity_version.xml", "/Users/<USER>/Downloads/skybound-gs-main/skybound-gs-main/Assets/Plugins/Android/AndroidManifest.xml", "/Users/<USER>/Downloads/skybound-gs-main/skybound-gs-main/Assets/Plugins/Demigiant/DOTween/readme.txt", "/Users/<USER>/Downloads/skybound-gs-main/skybound-gs-main/Assets/Plugins/Android/FirebaseApp.androidlib/res/values/google-services.xml"], "BuildActions": ["Compile", "Compile", "Compile", "Compile", "Compile", "Compile", "Compile", "Compile", "None", "None", "None", "None", "None", "None", "None", "None"], "Analyzers": [], "AdditionalFiles": [], "EditorConfigFiles": []}